'use client';

import React from 'react';
import { DraggableItem } from '@/components/DraggableItem';
import { useDragDropContext } from '@/components/DragDropProvider';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DragDropType, Attachment } from '@/utils/drag-drop-types';
import {
  Trash2,
  MoveVertical,
  Lock as LockIcon,
  RefreshCw,
} from 'lucide-react';

interface AttachmentCardProps {
  attachment: Attachment;
  contractId: string;
  onDelete: (attachmentId: string, contractId: string) => void;
}

export const AttachmentCard: React.FC<AttachmentCardProps> = ({
  attachment,
  contractId,
  onDelete,
}) => {
  const { dragOverTarget, isDragging } = useDragDropContext();

  const isLocked = attachment.is_existing && !attachment.is_replaced;
  const isReplaced = attachment.is_replaced;
  const isImmutable = isLocked || isReplaced;

  return (
    <DraggableItem
      id={attachment.id}
      data={{ id: attachment.id, type: DragDropType.Attachment, contractId }}
      disabled={isImmutable}
      className={`flex items-center py-2 px-3 rounded-md ${
        isLocked ? "bg-gray-50 border border-gray-200 opacity-75" :
        isReplaced ? "bg-orange-50 border border-orange-200 opacity-80" :
        dragOverTarget === attachment.id ? "bg-blue-50 border border-blue-200" : "hover:bg-gray-100"
      }`}
    >
      <div className={`mr-2 ${isImmutable ? "text-gray-300" : "cursor-move text-gray-400"}`}>
        {isReplaced ? (
          <RefreshCw className="h-4 w-4 text-orange-500 opacity-70" />
        ) : (
          isLocked ? (
            <LockIcon className="h-4 w-4 opacity-70" />
          ) : (
            <MoveVertical className="h-4 w-4" />
          )
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className={`text-sm font-medium truncate ${
          isLocked ? 'text-gray-500' :
          isReplaced ? 'text-orange-700' : 'text-gray-900'
        }`}>
          {attachment.name}
        </p>
        <p className="text-xs text-gray-500">{attachment.type}</p>
      </div>
      <Badge variant={
        isLocked ? "secondary" :
        isReplaced ? "default" :
        attachment.moved ? "default" : "outline"
      } className={`mr-2 ${
        isLocked ? "bg-gray-100 text-gray-600" :
        isReplaced ? "bg-orange-100 text-orange-700" :
        attachment.moved ? "bg-blue-100 text-blue-700" : ""
      }`}>
        {isDragging === attachment.id
          ? 'Dragging'
          : isReplaced
            ? 'Updated'
            : isLocked
              ? 'Existing'
              : attachment.moved
                ? 'Manual'
                : `${attachment.confidence}%`}
      </Badge>
      <Button
        variant="ghost"
        size="sm"
        className={`h-8 w-8 p-0 ${isImmutable ? "text-gray-300 cursor-not-allowed" : "text-gray-400 hover:text-red-500"}`}
        onClick={() => !isImmutable && onDelete(attachment.id, contractId)}
        disabled={isImmutable}
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </DraggableItem>
  );
};
