'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { DraggableItem } from '@/components/DraggableItem';
import { useDragDropContext } from '@/components/DragDropProvider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DragDropType, Contract } from '@/utils/drag-drop-types';
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Clock,
  DollarSign,
  Users,
  Trash2,
  MoveVertical,
  Lock as LockIcon,
  RefreshCw,
} from 'lucide-react';

// Color mapping for contract drop targets
const getContractDropColors = (isOver: boolean, isAnyDragging: boolean): string => {
  if (isOver) {
    return 'bg-blue-100 border-blue-400 shadow-lg scale-[1.02] transform';
  }
  if (isAnyDragging) {
    return 'hover:border-blue-300 hover:bg-blue-50';
  }
  return 'border-gray-200';
};

interface ContractCardProps {
  contract: Contract;
  isExpanded: boolean;
  onToggle: () => void;
  onDelete: (contractId: string) => void;
  onDeleteAttachment: (attachmentId: string, contractId: string) => void;
}

export const ContractCard: React.FC<ContractCardProps> = ({
  contract,
  isExpanded,
  onToggle,
  onDelete,
  onDeleteAttachment,
}) => {
  const { dragOverTarget, isAnyDragging, isDragging, getDropPreview } = useDragDropContext();

  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: contract.id,
    data: { id: contract.id, type: DragDropType.Contract },
    disabled: false,
  });

  const isLocked = contract.is_existing && !contract.is_replaced;
  const isReplaced = contract.is_replaced;

  return (
    <div
      ref={setNodeRef}
      className={`border-2 rounded-lg overflow-hidden transition-all duration-200 relative ${getContractDropColors(isOver, isAnyDragging)}`}
    >
      {/* Drop overlay when dragging over */}
      {isOver && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-10 pointer-events-none z-10 flex items-center justify-center">
          <div className="bg-blue-500 text-white px-4 py-2 rounded-lg font-medium shadow-lg">
            {getDropPreview({ id: contract.id, type: DragDropType.Contract }).message}
          </div>
        </div>
      )}

      <DraggableItem
        id={contract.id}
        data={{ id: contract.id, type: DragDropType.Contract }}
        disabled={isLocked || isReplaced}
        className={`flex items-center p-4 border-b ${
          isLocked ? 'bg-gray-50 border-gray-200 opacity-75' :
          isReplaced ? 'bg-orange-50 border-orange-200 opacity-80' : ''
        } ${
          isAnyDragging ? 'cursor-move' : 'cursor-pointer hover:bg-gray-50'
        }`}
      >
        <div
          className="flex items-center w-full"
          onClick={() => isAnyDragging ? null : onToggle()}
        >
          <div className="mr-2">
            {isExpanded ? (
              <ChevronDown className={`h-5 w-5 ${
                isLocked ? 'text-gray-400' :
                isReplaced ? 'text-orange-500' : 'text-gray-600'
              }`} />
            ) : (
              <ChevronRight className={`h-5 w-5 ${
                isLocked ? 'text-gray-400' :
                isReplaced ? 'text-orange-500' : 'text-gray-600'
              }`} />
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <h4 className={`font-medium ${
                  isLocked ? 'text-gray-500' :
                  isReplaced ? 'text-orange-700' : 'text-gray-900'
                }`}>
                  {contract.name}
                </h4>
                <Badge variant={
                  isLocked ? "secondary" :
                  isReplaced ? "default" :
                  contract.moved ? "default" : "outline"
                } className={
                  isLocked ? "bg-gray-100 text-gray-600" :
                  isReplaced ? "bg-orange-100 text-orange-700" :
                  contract.moved ? "bg-blue-100 text-blue-700" : ""
                }>
                  {isDragging === contract.id
                    ? 'Dragging'
                    : isReplaced
                      ? 'Updated'
                      : isLocked
                        ? 'Existing'
                        : contract.moved
                          ? 'Manual'
                          : `${contract.confidence}%`}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {contract.contractType}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-8 w-8 p-0 ${
                    isLocked || isReplaced ? "text-gray-300 cursor-not-allowed" : "text-gray-400 hover:text-red-500"
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!isLocked && !isReplaced) {
                      onDelete(contract.id);
                    }
                  }}
                  disabled={isLocked || isReplaced}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DraggableItem>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="p-4 bg-gray-50">
          <Card className={`mb-4 border-l-4 ${isReplaced ? 'border-l-orange-500 bg-orange-50' : 'border-l-blue-500'}`}>
            <CardHeader className="pb-2">
              <CardTitle className={`text-lg ${isReplaced ? 'text-orange-700' : ''}`}>
                {contract.name}
                {isReplaced && <span className="ml-2 text-sm text-orange-500">(Updated)</span>}
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Effective Date</p>
                    <p className="text-sm font-medium">{contract.startDate}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Expiry Date</p>
                    <p className="text-sm font-medium">{contract.endDate}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Contract Value</p>
                    <p className="text-sm font-medium">{contract.contractValue || ''}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">Parties</p>
                    <p className="text-sm font-medium break-words whitespace-normal">
                      {contract.parties.join(", ")}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attachments Section */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-700 mb-2">
              Attachments ({contract.attachments.length})
            </h5>
            {contract.attachments.map((attachment) => {
              const isAttachmentLocked = attachment.is_existing && !attachment.is_replaced;
              const isAttachmentReplaced = attachment.is_replaced;
              const isImmutable = isAttachmentLocked || isAttachmentReplaced;

              return (
                <DraggableItem
                  key={attachment.id}
                  id={attachment.id}
                  data={{ id: attachment.id, type: DragDropType.Attachment, contractId: contract.id }}
                  disabled={isImmutable}
                  className={`flex items-center py-2 px-3 rounded-md ${
                    isAttachmentLocked ? "bg-gray-50 border border-gray-200 opacity-75" :
                    isAttachmentReplaced ? "bg-orange-50 border border-orange-200 opacity-80" :
                    dragOverTarget === attachment.id ? "bg-blue-50 border border-blue-200" : "hover:bg-gray-100"
                  }`}
                >
                  <div className={`mr-2 ${isImmutable ? "text-gray-300" : "cursor-move text-gray-400"}`}>
                    {isAttachmentReplaced ? (
                      <RefreshCw className="h-4 w-4 text-orange-500 opacity-70" />
                    ) : (
                      isAttachmentLocked ? (
                        <LockIcon className="h-4 w-4 opacity-70" />
                      ) : (
                        <MoveVertical className="h-4 w-4" />
                      )
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium truncate ${
                      isAttachmentLocked ? 'text-gray-500' :
                      isAttachmentReplaced ? 'text-orange-700' : 'text-gray-900'
                    }`}>
                      {attachment.name}
                    </p>
                    <p className="text-xs text-gray-500">{attachment.type}</p>
                  </div>
                  <Badge variant={
                    isAttachmentLocked ? "secondary" :
                    isAttachmentReplaced ? "default" :
                    attachment.moved ? "default" : "outline"
                  } className={`mr-2 ${
                    isAttachmentLocked ? "bg-gray-100 text-gray-600" :
                    isAttachmentReplaced ? "bg-orange-100 text-orange-700" :
                    attachment.moved ? "bg-blue-100 text-blue-700" : ""
                  }`}>
                    {isDragging === attachment.id
                      ? 'Dragging'
                      : isAttachmentReplaced
                        ? 'Updated'
                        : isAttachmentLocked
                          ? 'Existing'
                          : attachment.moved
                            ? 'Manual'
                            : `${attachment.confidence}%`}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`h-8 w-8 p-0 ${isImmutable ? "text-gray-300 cursor-not-allowed" : "text-gray-400 hover:text-red-500"}`}
                    onClick={() => !isImmutable && onDeleteAttachment(attachment.id, contract.id)}
                    disabled={isImmutable}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </DraggableItem>
              );
            })}
            {contract.attachments.length === 0 && (
              <div className="text-sm text-gray-500 italic py-2">
                No attachments found. Drag documents here to add.
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
