'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { ContractCard } from '@/components/ContractCard';
import { useDragDropContext } from '@/components/DragDropProvider';
import { Badge } from '@/components/ui/badge';
import { DragDropType, Provider } from '@/utils/drag-drop-types';
import {
  ChevronDown,
  ChevronRight,
  Building,
} from 'lucide-react';

// Color mapping for provider drop targets
const getProviderDropColors = (isOver: boolean, isAnyDragging: boolean): string => {
  if (isOver) {
    return 'border-green-400 bg-green-100 shadow-lg scale-[1.01] transform';
  }
  if (isAnyDragging) {
    return 'border-green-300 hover:border-green-400 hover:bg-green-50';
  }
  return '';
};

const getProviderHeaderColors = (isOver: boolean, isAnyDragging: boolean): string => {
  if (isOver) {
    return 'bg-green-100 border-green-200';
  }
  if (isAnyDragging) {
    return 'bg-indigo-50/50 hover:bg-green-50';
  }
  return 'bg-indigo-50/50';
};

interface ProviderCardProps {
  provider: Provider;
  isExpanded: boolean;
  onToggle: () => void;
  expandedContracts: Record<string, boolean>;
  onToggleContract: (contractId: string) => void;
  onDeleteContract: (contractId: string) => void;
  onDeleteAttachment: (attachmentId: string, contractId: string) => void;
}

export const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  isExpanded,
  onToggle,
  expandedContracts,
  onToggleContract,
  onDeleteContract,
  onDeleteAttachment,
}) => {
  const { dragOverTarget, isAnyDragging, isDragging, getDropPreview } = useDragDropContext();

  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: provider.id,
    data: { id: provider.id, type: DragDropType.Provider },
    disabled: false,
  });

  return (
    <div
      ref={setNodeRef}
      className={`relative border rounded-lg overflow-hidden bg-white shadow-sm transition-all duration-200 ${getProviderDropColors(isOver, isAnyDragging)}`}
    >
      {/* Drop overlay when dragging over provider */}
      {isOver && (
        <div className="absolute inset-0 bg-green-500 bg-opacity-10 pointer-events-none z-10 flex items-center justify-center">
          <div className="bg-green-500 text-white px-4 py-2 rounded-lg font-medium shadow-lg">
            {getDropPreview({ id: provider.id, type: DragDropType.Provider }).message}
          </div>
        </div>
      )}

      {/* Provider Header - Clickable to expand */}
      <div
        className={`flex items-center p-4 border-b cursor-pointer ${getProviderHeaderColors(isOver, isAnyDragging)}`}
        onClick={onToggle}
      >
        <div className="mr-3">
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-gray-600" />
          ) : (
            <ChevronRight className="h-5 w-5 text-gray-600" />
          )}
        </div>
        <Building className="h-5 w-5 text-indigo-600 mr-3" />
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{provider.name}</h3>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
            {provider.contracts.length} contract{provider.contracts.length !== 1 ? 's' : ''}
          </Badge>
        </div>
      </div>

      {/* Provider Expanded Content - Contracts */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {provider.contracts?.map((contract) => (
            <ContractCard
              key={contract.id}
              contract={contract}
              isExpanded={expandedContracts[contract.id] || false}
              onToggle={() => onToggleContract(contract.id)}
              onDelete={onDeleteContract}
              onDeleteAttachment={onDeleteAttachment}
            />
          ))}
          {provider.contracts.length === 0 && (
            <div className="text-sm text-gray-500 italic py-4 text-center">
              No contracts found. Drag documents here to create new contracts.
            </div>
          )}
        </div>
      )}
    </div>
  );
};
