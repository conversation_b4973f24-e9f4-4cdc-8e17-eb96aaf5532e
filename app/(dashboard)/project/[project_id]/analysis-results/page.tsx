"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { DragDropType } from "@/utils/drag-drop-types"
import { DragDropProvider, useDragDropContext } from "@/components/DragDropProvider"
import { DraggableItem } from "@/components/DraggableItem"
import { DroppableArea } from "@/components/DroppableArea"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ChevronRight,
  ChevronDown,
  FileText,
  File,
  Calendar,
  DollarSign,
  Users,
  Clock,
  ArrowRight,
  AlertTriangle,
  Trash2,
  MoveVertical,
  Building,
  Filter,
  Lock as LockIcon,
  <PERSON>freshC<PERSON>,
  CheckCircle,
  Edit3,
} from "lucide-react"
import { useFileAnalysis, useFileAnalysisStatus } from "@/api/file-analysis"
import { useProjectFileStore } from "@/store/project-file-store"
import { useTriggerIngestionContractsAndAttachments } from "@/api/document";
import { toast } from "react-toastify"

// Contract types
const contractTypes = ["All", "AMS", "ADM", "Infrastructure", "Support Services", "Other"]




function AnalysisResultsContent() {
  const {
    isDragging,
    dragOverTarget,
    isAnyDragging,
    getDropPreview,
  } = useDragDropContext();
  // Router
  const router = useRouter();

  // Mutations
  const { mutate: triggerIngestionContractsAndAttachments } = useTriggerIngestionContractsAndAttachments();

  // Store
  const files = useProjectFileStore((state) => state.files); // Get uploaded files from the store
  const setFiles = useProjectFileStore((state) => state.setFiles);
  // UI State
  const [expandedProviders, setExpandedProviders] = useState<Record<string, boolean>>({});
  const [expandedContracts, setExpandedContracts] = useState<Record<string, boolean>>({});
  const [selectedContractType, setSelectedContractType] = useState<string>("All");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [polling, setPolling] = useState(false);
  const [isFileAnalysisError, setIsFileAnalysisError] = useState(false);
  const [editingContractType, setEditingContractType] = useState<string | null>(null);

  // Data State
  const [taskId, setTaskId] = useState<string | null>(null);
  const [providerData, setProviderData] = useState<any[]>([]);
  const [unmappedDocuments, setUnmappedDocuments] = useState<any[]>([]);

  // Drag and drop state will be passed down from DragDropProvider

  const projectId = useProjectFileStore((state) => state.projectId);

  const fileAnalysisMutation = useFileAnalysis(projectId);
  const { data: analysisStatus, isLoading: isStatusLoading } = useFileAnalysisStatus(taskId as string, {
    enabled: !!taskId,
    refetchInterval: polling ? 2000 : false,
  });


  useEffect(() => {
    if (files.length === 0) {
      console.error("No files found in the store");
      return;
    }

    // Create FormData from files in the store
    const formData = new FormData();

    const replacementTuples: [string, string][] = [];
    files.forEach(({ file, relativePath, replacedFile }) => {

      if (replacedFile) {
        replacementTuples.push([relativePath, replacedFile]);
      } else {
        formData.append("files", file, relativePath);
      }
    });

    if (replacementTuples.length > 0) {
      formData.append('is_existing_files', JSON.stringify(replacementTuples));
    }

    setIsAnalyzing(true);
    setProviderData([]); // Reset old data

    fileAnalysisMutation.mutate(formData, {
      onSuccess: (response) => {
        setTaskId(response.task_id);
        setPolling(true); // Start polling for task status
      },
      onError: () => {
        setIsFileAnalysisError(true);
        setIsAnalyzing(false);
        toast.error("Failed to analyze files. Please try again.");
        console.error("Failed to fetch file analysis data");
      },
    });
  }, [files]);

  useEffect(() => {
    if (!analysisStatus || isStatusLoading) return;

    if (analysisStatus.error !== null) {
      setIsAnalyzing(false);
      setPolling(false);
      toast.error("File analysis failed, please try again");
      return;
    }

    if (analysisStatus.status === "completed") {
      setIsAnalyzing(false);

      if (analysisStatus.result) {
        setPolling(false);
        const normalized = analysisStatus.result.contract_data_results.map((provider, index) => ({
          id: `provider-${index}`,
          name: provider.vendors.join(" & "),
          contracts: provider.main_contracts.map((contract, contractIndex) => {
            const contractTypeBase = contract.contract_type.split(" - ")[0];
            const mappedContractType = contractTypes.includes(contractTypeBase)
              ? contractTypeBase
              : "Other";

            return {
              id: `contract-${index}-${contractIndex}`,
              name: contract.file_name,
              type: contract.contract_type,
              contractType: mappedContractType,
              startDate: contract.start_date,
              endDate: contract.end_date,
              contractValue: "",
              parties: provider.vendors,
              confidence: Math.round(contract.confidence_score * 100),
              status: "processed",
              is_replaced: contract.is_replaced || false,
              is_existing: contract.is_existing || false,
              attachments: contract.attachments?.map((attachment, attachmentIndex) => ({
                  id: `attachment-${index}-${contractIndex}-${attachmentIndex}`,
                  name: attachment.attachment_file_name,
                  type: "Attachment", // Hardcoded value instead of attachment.type
                  confidence: Math.round(attachment.confidence * 100),
                  is_existing: attachment.is_existing || false,
                  is_replaced: attachment.is_replaced || false,
                })) || [],
              missingDocuments: [],
            };
          }),
        }));

        setProviderData(normalized);

        const normalizedUnmapped = analysisStatus.result.unmapped_attachments.map((doc, index) => ({
          id: `unmapped-${index}`,
          name: doc,
          type: "Unknown",
          status: "processed",
          confidence: 0,
          is_existing: false,
        }));

        setUnmappedDocuments(normalizedUnmapped);
      }
    }

    if (analysisStatus.status === "error") {
      console.error("❌ Analysis failed:", analysisStatus.error);
      setIsAnalyzing(false);
      setIsFileAnalysisError(true);
      toast.error("File analysis failed. Please try again.");
      setPolling(false);
    }
  }, [analysisStatus, isStatusLoading]);




  const toggleProvider = (providerId: string) => {
    setExpandedProviders((prev) => ({
      ...prev,
      [providerId]: !prev[providerId],
    }))
  }

  const toggleContract = (contractId: string) => {
    setExpandedContracts((prev) => ({
      ...prev,
      [contractId]: !prev[contractId],
    }))
  }

  const ingestData = () => {
    if (!projectId) {
      toast.error("Project ID not found");
      return;
    }

    const payload = {
      providers: providerData.map(provider => ({
        providers: provider.name,
        main_contracts: provider.contracts.map(contract => ({
          file_name: contract.name,
          file_size: files.find(file => file.file.webkitRelativePath === contract.name)?.file.size || 0,
          contract_value: "",
          contract_type: contract.contractType,
          start_date: contract.startDate,
          end_date: contract.endDate,
          is_already_existed: false,
          attachments: contract.attachments.map(attachment => ({
            file_name: attachment.name,
            file_size: files.find(file => file.file.webkitRelativePath === attachment.name)?.file.size || 0,
            is_already_existed: attachment.is_existing || false, // Use the is_existing flag from the attachment
            is_replaced: attachment.is_replaced || false, // Use the is_replaced flag from the attachment
          })),
        })),
      })),
      unmapped_documents: unmappedDocuments.map(doc => ({
        file_name: doc.name,
        file_size: files.find(file => file.file.webkitRelativePath === doc.name)?.file.size || 0,
        is_already_existed: doc.is_existing || false,
        is_replaced: doc.is_replaced || false,
      })),
    };


    triggerIngestionContractsAndAttachments(
      {
        projectId,
        contract_attachment_data: payload
      },
      {
        onSuccess: () => {
          toast.success("Project created and files uploaded successfully!");
          router.push(`/project/${projectId}/file-management`);
        },
        onError: (error) => {
          toast.error(`Error in Ingesting Files: ${error.message}`);
        },
      }
    );
  }

  // Get document icon based on type
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case "MSA":
        return <FileText className="h-5 w-5 text-blue-600" />
      case "SOW":
        return <FileText className="h-5 w-5 text-green-600" />
      case "SLA":
        return <FileText className="h-5 w-5 text-purple-600" />
      case "CSA":
        return <FileText className="h-5 w-5 text-orange-600" />
      default:
        return <File className="h-5 w-5 text-gray-600" />
    }
  }

  // Get confidence level badge
  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 90) return "bg-green-100 text-green-800"
    if (confidence >= 75) return "bg-amber-100 text-amber-800"
    return "bg-red-100 text-red-800"
  }





  // Delete a document
  const handleDeleteDocument = (id: string, type: "unmapped" | "attachment" | "contract", contractId?: string) => {
    if (type === "unmapped") {
      setUnmappedDocuments(unmappedDocuments.filter((doc) => doc.id !== id))
    } else if (type === "attachment" && contractId) {
      const updatedProviders = providerData.map((provider) => {
        return {
          ...provider,
          contracts: provider.contracts.map((contract) => {
            if (contract.id === contractId) {
              return {
                ...contract,
                attachments: contract.attachments.filter((att) => att.id !== id),
              }
            }
            return contract
          }),
        }
      })
      setProviderData(updatedProviders)
    } else if (type === "contract") {
      const updatedProviders = providerData.map((provider) => {
        return {
          ...provider,
          contracts: provider.contracts.filter((contract) => contract.id !== id),
        }
      })
      setProviderData(updatedProviders)
    }
  }

  // Handle contract type change
  const handleContractTypeChange = (contractId: string, newType: string) => {
    const updatedProviders = providerData.map((provider) => {
      return {
        ...provider,
        contracts: provider.contracts.map((contract) => {
          if (contract.id === contractId) {
            return {
              ...contract,
              contractType: newType,
            }
          }
          return contract
        }),
      }
    })

    setProviderData(updatedProviders)
    setEditingContractType(null) // Close the edit mode
  }

  // Filter providers based on selected contract type
  const filteredProviderData =
    selectedContractType === "All"
      ? providerData
      : providerData.filter((provider) =>
        provider.contracts.some(
          (contract) => contract.contractType === selectedContractType
        )
      );

  const totalContracts = filteredProviderData.reduce(
    (acc, provider) => acc + (provider.contracts?.length || 0),
    0
  );

  const totalAttachments = filteredProviderData.reduce(
    (acc, provider) =>
      acc +
      (provider.contracts?.reduce(
        (contractAcc, contract) => contractAcc + (contract.attachments?.length || 0),
        0
      ) || 0),
    0
  );

  const totalDocuments = totalContracts + totalAttachments + (unmappedDocuments?.length || 0);

  // Calculate total documents and other stats
  const totalProviders = filteredProviderData.length

  if (isAnalyzing) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-center h-[60vh]">
            <div className="h-12 w-12 rounded-full border-4 border-t-[#21BF61] border-r-transparent border-b-transparent border-l-transparent animate-spin mb-4"></div>
            <h2 className="text-xl font-medium">Analyzing uploaded files...</h2>
            <p className="text-gray-500 mt-2">Identifying main contracts and attachments</p>
          </div>
        </div>
      </div>
    )
  }

  if (fileAnalysisMutation.isPending) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>
  }

  if (isFileAnalysisError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="border-red-200 bg-red-50">
          <AlertTitle className="text-red-800">Error</AlertTitle>
          <AlertDescription className="text-red-700">
            Failed to load analysis data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!providerData || providerData.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center text-center p-10">
        <div>
          <CheckCircle className="h-16 w-16 text-[#21BF61] mx-auto mb-4" />
          <h2 className="text-2xl font-medium mb-2">Nothing New to Process</h2>
          <p className="text-gray-500 mb-6">All documents in your project have already been analyzed. No new documents require processing at this time.</p>
          <Button onClick={() => router.push(`/project/${projectId}/file-management`)}>
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <DragDropProvider
      providers={providerData}
      unmappedDocuments={unmappedDocuments}
      onDataUpdate={(updatedProviders, updatedUnmappedDocuments) => {
        setProviderData(updatedProviders);
        setUnmappedDocuments(updatedUnmappedDocuments);
      }}
      onError={(error) => {
        console.error("Drag and drop error:", error);
        toast.error(error);
      }}
    >
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-12">
        <div className="container mx-auto px-4">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          <div className="flex flex-col md:flex-row justify-between items-start mb-8 gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">Document Analysis Complete</h1>
              <p className="text-gray-600">
                We've analyzed your {totalDocuments} documents and identified {totalProviders} providers with{" "}
                {totalContracts} main contracts.
              </p>
            </div>
          </div>

          {/* Stats Bar */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Total Documents</p>
                    <p className="text-2xl font-bold">{totalDocuments}</p>
                  </div>
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Providers</p>
                    <p className="text-2xl font-bold">{totalProviders}</p>
                  </div>
                  <Building className="h-8 w-8 text-indigo-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Main Contracts</p>
                    <p className="text-2xl font-bold">{totalContracts}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Unmapped Documents</p>
                    <p className="text-2xl font-bold">{unmappedDocuments.length}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-amber-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Instructions Alert */}
          <Alert className="mb-4 border-blue-200 bg-blue-50">
            <FileText className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">Document Organization</AlertTitle>
            <AlertDescription className="text-blue-700">
              We've automatically organized your documents by provider, main contracts, and their attachments. You can
              drag and drop documents to rearrange them, move contracts between providers or to unmapped section, assign unmapped documents, or
              drag attachments and unmapped documents to provider areas to convert them into new contracts.
            </AlertDescription>
          </Alert>

          {/* Dragging Indicator */}
          {isAnyDragging && (
            <Alert className="mb-4 border-purple-200 bg-purple-50">
              <MoveVertical className="h-4 w-4 text-purple-600" />
              <AlertTitle className="text-purple-800">Dragging in Progress</AlertTitle>
              <AlertDescription className="text-purple-700">
                Drag your item over a contract card (blue highlight) to move it, over a provider area (green highlight) to convert attachments and unmapped documents to contracts, or over the unmapped section (blue highlight) to convert contracts to unmapped documents.
                {dragOverTarget && <span className="font-bold"> Currently over: {dragOverTarget}</span>}
              </AlertDescription>
            </Alert>
          )}

          {/* Contract Type Filters */}
          <div className="mb-6 bg-white p-4 rounded-lg border shadow-sm">
            <div className="flex items-center mb-3">
              <Filter className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="font-medium text-gray-800">Filter by Contract Type</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {contractTypes.map((type) => (
                <Button
                  key={type}
                  variant={selectedContractType === type ? "default" : "outline"}
                  className={`${selectedContractType === type
                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                    : "text-gray-700 hover:bg-purple-50"
                    }`}
                  size="sm"
                  onClick={() => setSelectedContractType(type)}
                >
                  {type}
                  {selectedContractType === type && (
                    <Badge className="ml-2 bg-white text-purple-700 border-purple-300">
                      {type === "All"
                        ? providerData.reduce((acc, provider) => acc + (provider.contracts?.length || 0), 0)
                        : providerData.reduce(
                          (acc, provider) =>
                            acc +
                            (provider.contracts?.filter((contract) => contract.contractType === type).length || 0),
                          0
                        )}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>

          {/* Provider Tree View */}
          <div className="space-y-6">
            {filteredProviderData.map((provider) => (
              <ProviderCard
                key={provider.id}
                provider={provider}
                isExpanded={expandedProviders[provider.id] || false}
                onToggle={() => toggleProvider(provider.id)}
                expandedContracts={expandedContracts}
                onToggleContract={toggleContract}
                onDeleteContract={(contractId) => handleDeleteDocument(contractId, "contract")}
                onDeleteAttachment={(attachmentId, contractId) => handleDeleteDocument(attachmentId, "attachment", contractId)}
              />
            ))}
            {filteredProviderData.length === 0 && (
              <div className="text-center py-8 border rounded-lg bg-white">
                <AlertTriangle className="h-12 w-12 text-amber-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">No matching contracts found</h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  There are no contracts of type "{selectedContractType}" in the current dataset. Try selecting a
                  different contract type filter.
                </p>
                <Button
                  className="mt-4 bg-purple-600 hover:bg-purple-700"
                  onClick={() => setSelectedContractType("All")}
                >
                  Show All Contracts
                </Button>
              </div>
            )}
          </div>

          {/* Unmapped Documents Section */}
          <div className="mt-8 border rounded-lg overflow-hidden bg-white shadow-sm">
            <div className="p-4 bg-amber-50 border-b border-amber-200 flex justify-between items-center">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-amber-600 mr-2" />
                <h3 className="font-medium">Unmapped Documents</h3>
                <span className="ml-2 text-sm text-amber-700">
                  ({unmappedDocuments.length} document{unmappedDocuments.length !== 1 ? "s" : ""})
                </span>
              </div>
              <div className="text-sm text-amber-700">Drag these documents to their respective contracts or drag contracts here to convert them</div>
            </div>

            <DroppableArea
              id="unmapped-area"
              data={{ id: "unmapped-area", type: DragDropType.Unmapped }}
              className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 relative"
            >
              {/* Drop zone indicator when dragging over unmapped area */}
              {dragOverTarget === "unmapped-area" && isDragging && (
                <div className="absolute inset-0 bg-red-500 bg-opacity-10 pointer-events-none z-10 flex items-center justify-center">
                  <div className="bg-red-500 text-white px-4 py-2 rounded-lg font-medium shadow-lg">
                    {getDropPreview({ id: "unmapped-area", type: DragDropType.Unmapped }).message}
                  </div>
                </div>
              )}
              {unmappedDocuments?.map((doc) => (
                <DraggableItem
                  key={doc.id}
                  id={doc.id}
                  data={{ id: doc.id, type: DragDropType.Unmapped }}
                  className={`border rounded-md p-3 flex items-center ${
                    dragOverTarget === doc.id ? "bg-blue-50 border-blue-200" : "hover:bg-gray-50"
                  }`}
                >
                  <div className="mr-2 cursor-move text-gray-400">
                    <MoveVertical className="h-4 w-4" />
                  </div>
                  <div className="mr-3">
                    <File className="h-4 w-4 text-gray-500" />
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{doc.name}</div>
                    <div className="text-xs text-gray-500">{doc.type}</div>
                  </div>
                  <Badge className={`mr-2 ${
                    doc.moved
                      ? 'bg-green-100 text-green-800 border-green-200'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {isDragging === doc.id
                      ? 'Dragging'
                      : doc.moved
                        ? 'Manual'
                        : 'Unknown'
                    }
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                    onClick={() => handleDeleteDocument(doc.id, "unmapped")}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </DraggableItem>
              ))}
              {unmappedDocuments.length === 0 && (
                <div className="col-span-full text-center py-6 text-gray-500 italic">
                  No unmapped documents found. All documents have been successfully mapped to contracts.
                </div>
              )}
            </DroppableArea>
          </div>

          <div className="mt-8 flex justify-end">
            <Button onClick={ingestData} className="bg-[#21BF61] hover:bg-[#21BF61]/90">
              Import Data
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
          </motion.div>
        </div>
      </div>
    </DragDropProvider>
  )
}

// Main component that wraps the content with DragDropProvider
export default function AnalysisResultsPage() {
  return <AnalysisResultsContent />;
}
