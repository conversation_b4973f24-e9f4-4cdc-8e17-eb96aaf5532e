'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { DropTarget, DragDropType } from '@/utils/drag-drop-types';

// Color mapping for different drop target types
const getDropTargetColors = (type: DragDropType): string => {
  switch (type) {
    case DragDropType.Provider:
      return 'bg-green-100 border-green-400'; // Strong green for provider
    case DragDropType.Contract:
    case DragDropType.Unmapped:
      return 'bg-blue-100 border-blue-400'; // Strong blue for contract
    default:
      return 'bg-gray-100 border-gray-400'; // Default fallback
  }
};

interface DroppableAreaProps {
  id: string;
  data: DropTarget;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export const DroppableArea: React.FC<DroppableAreaProps> = ({
  id,
  data,
  children,
  className = '',
  disabled = false,
}) => {
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id,
    data,
    disabled,
  });

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${isOver ? getDropTargetColors(data.type) : ''}`}
    >
      {children}
    </div>
  );
};
